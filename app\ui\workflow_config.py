"""
工作流配置页面

该模块提供单个工作流的详细配置功能，包括：
- 工作流基本信息展示
- 参数编辑
- 输入输出目录设置
- 运行控制
"""

import streamlit as st
import os
import json
from datetime import datetime
from typing import Dict, Any, List
import requests

def safe_get_workflow_attr(workflow, attr_path, default=None):
    """安全获取工作流属性，支持dict和对象两种格式"""
    try:
        # 按路径分割属性
        attrs = attr_path.split('.')
        current = workflow
        
        for attr in attrs:
            if isinstance(current, dict):
                current = current.get(attr, default)
            else:
                current = getattr(current, attr, default)
            
            if current is None:
                return default
        
        return current
    except:
        return default

def extract_notes_from_workflow(workflow_json: Dict[str, Any]) -> str:
    """从工作流JSON中提取Note或Markdown Note节点的内容"""
    notes = []
    
    for node_id, node_data in workflow_json.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "")
            
            # 检查是否是Note或Markdown Note类型的节点
            if "note" in class_type.lower() or "markdown" in class_type.lower():
                # 查找文本内容
                inputs = node_data.get("inputs", {})
                
                # 常见的文本字段名
                text_fields = ["text", "content", "note", "description", "markdown"]
                
                for field in text_fields:
                    if field in inputs:
                        text_content = inputs[field]
                        if isinstance(text_content, str) and text_content.strip():
                            notes.append(text_content.strip())
                        break
    
    return "\n\n".join(notes) if notes else "暂无简介信息"

def classify_workflow_type(workflow_json: Dict[str, Any]) -> str:
    """根据工作流内容分类工作流类型"""
    has_image_input = False
    has_text_input = False
    has_video_output = False
    has_image_output = False
    
    for node_id, node_data in workflow_json.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "").lower()
            
            # 检查输入类型
            if "loadimage" in class_type or "imageload" in class_type:
                has_image_input = True
            elif "cliptext" in class_type or "textinput" in class_type:
                has_text_input = True
            
            # 检查输出类型
            if "videosave" in class_type or "videoout" in class_type or "animatediff" in class_type:
                has_video_output = True
            elif "imagesave" in class_type or "imageout" in class_type:
                has_image_output = True
    
    # 分类逻辑
    if has_image_input and has_image_output:
        return "图生图"
    elif has_image_input and has_video_output:
        return "图生视频"
    elif has_text_input and has_video_output:
        return "文生视频"
    elif has_text_input and has_image_output:
        return "文生图"
    else:
        return "其他"

def is_editable_node(class_type: str) -> bool:
    """判断节点是否可编辑"""
    editable_types = {
        # 文本输入相关
        "CLIPTextEncode", "CLIPTextEncodeSDXL", "TripleCLIPTextEncode",
        "EmptyLatentImage", "LatentUpscale", "LatentUpscaleBy",
        "KSampler", "KSamplerAdvanced", "SamplerCustom",
        # 图像处理
        "ImageResize", "ImageScale", "ImageScaleBy",
        # ControlNet
        "ControlNetApply", "ControlNetApplyAdvanced",
        # Lora加载
        "LoraLoader", "LoraLoaderModelOnly",
        # 其他常见可编辑节点
        "SaveImage", "SaveVideo", "VAEEncode", "VAEDecode"
    }
    
    return any(edit_type.lower() in class_type.lower() for edit_type in editable_types)

def extract_editable_params_from_node(node_id: str, class_type: str, node_title: str, inputs: Dict) -> List[Dict]:
    """从节点中提取可编辑的参数"""
    params = []
    
    # 定义常见的可编辑参数类型
    editable_param_types = {
        str: "text",
        int: "number", 
        float: "number",
        bool: "checkbox"
    }
    
    for param_name, param_value in inputs.items():
        # 跳过引用其他节点的参数
        if isinstance(param_value, list) and len(param_value) == 2:
            continue
            
        param_type = type(param_value)
        
        if param_type in editable_param_types:
            param_info = {
                "node_id": node_id,
                "node_type": class_type,
                "node_title": node_title,
                "param_name": param_name,
                "param_type": editable_param_types[param_type],
                "current_value": param_value,
                "param_path": f"{node_id}.inputs.{param_name}"
            }
            
            # 为特定参数添加额外信息
            if param_name.lower() in ["seed", "noise_seed"]:
                param_info["param_type"] = "seed"
            elif param_name.lower() in ["steps", "sampling_steps"]:
                param_info["min_value"] = 1
                param_info["max_value"] = 100
            elif param_name.lower() in ["cfg", "guidance_scale"]:
                param_info["min_value"] = 1.0
                param_info["max_value"] = 30.0
                param_info["step"] = 0.1
            elif param_name.lower() in ["width", "height"]:
                param_info["min_value"] = 64
                param_info["max_value"] = 2048
                param_info["step"] = 64
            
            params.append(param_info)
    
    return params

def analyze_workflow_nodes(workflow_json: Dict[str, Any]) -> Dict[str, List[Dict]]:
    """分析工作流节点，提取可编辑的参数"""
    editable_nodes = []
    
    for node_id, node_data in workflow_json.items():
        if isinstance(node_data, dict):
            class_type = node_data.get("class_type", "")
            node_title = node_data.get("_meta", {}).get("title", class_type)
            inputs = node_data.get("inputs", {})
            
            # 检查是否是可编辑节点
            if is_editable_node(class_type):
                editable_params = extract_editable_params_from_node(node_id, class_type, node_title, inputs)
                if editable_params:
                    editable_nodes.append({
                        "node_id": node_id,
                        "class_type": class_type,
                        "title": node_title,
                        "parameters": editable_params
                    })
    
    return {"editable_nodes": editable_nodes}

def select_folder_with_dialog():
    """选择文件夹对话框（简化版本）"""
    # 注意：这是一个简化的实现，实际应用中可能需要使用tkinter或其他方式
    # 在Streamlit中直接返回None，让用户手动输入路径
    return None

def show_workflow_config_page(workflow_key: str):
    """显示工作流配置页面"""
    st.title(f"🎯 工作流配置：{workflow_key}")
    
    # 从后端获取工作流数据
    try:
        response = requests.get(f"http://localhost:18001/workflows/{workflow_key}")
        if response.status_code == 200:
            workflow_data = response.json()
            workflow = workflow_data.get("workflow")
            
            if not workflow:
                st.error("❌ 工作流数据为空")
                return
                
        else:
            st.error(f"❌ 获取工作流失败: {response.status_code}")
            return
            
    except Exception as e:
        st.error(f"❌ 连接后端失败: {str(e)}")
        return
    
    # 获取工作流JSON数据
    workflow_json = safe_get_workflow_attr(workflow, 'workflow_json', {})
    if not workflow_json:
        st.error("❌ 工作流JSON数据为空")
        return
    
    # 工作流基本信息
    st.header("📋 工作流信息")
    
    # 基本信息展示
    col1, col2 = st.columns(2)
    
    with col1:
        workflow_name = safe_get_workflow_attr(workflow, 'metadata.name', workflow_key)
        st.markdown(f"**名称：** {workflow_name}")
        
        workflow_type = classify_workflow_type(workflow_json)
        st.markdown(f"**类型：** {workflow_type}")
        
        tags = safe_get_workflow_attr(workflow, 'metadata.tags', [])
        if tags:
            tags_str = ", ".join(tags)
            st.markdown(f"**标签：** {tags_str}")
    
    with col2:
        version = safe_get_workflow_attr(workflow, 'metadata.version', '1.0')
        st.markdown(f"**版本：** {version}")
        
        author = safe_get_workflow_attr(workflow, 'metadata.author', '未知')
        st.markdown(f"**作者：** {author}")
        
        node_count = len(workflow_json)
        st.markdown(f"**节点数量：** {node_count}")
    
    # 工作流简介
    workflow_notes = extract_notes_from_workflow(workflow_json)
    if workflow_notes and workflow_notes != "暂无简介信息":
        st.markdown("**工作流简介：**")
        st.info(workflow_notes)
    else:
        st.markdown("**工作流简介：** 暂无简介信息")
    
    # 参数编辑部分
    st.markdown("---")
    st.header("⚙️ 参数配置")
    
    # 分析工作流节点
    node_analysis = analyze_workflow_nodes(workflow_json)
    editable_nodes = node_analysis.get("editable_nodes", [])
    
    if not editable_nodes:
        st.info("😊 此工作流使用默认参数，无需额外配置")
        workflow_params = {}
    else:
        st.markdown(f"发现 **{len(editable_nodes)}** 个可配置的节点")
        
        # 初始化参数存储
        if f"{workflow_key}_params" not in st.session_state:
            st.session_state[f"{workflow_key}_params"] = {}
        
        workflow_params = st.session_state[f"{workflow_key}_params"]
        
        # 为每个可编辑节点显示参数设置
        for node in editable_nodes:
            with st.expander(f"🔧 {node['title']} ({node['class_type']})"):
                for param in node['parameters']:
                    param_key = param['param_path']
                    param_name = param['param_name']
                    param_type = param['param_type']
                    current_value = param['current_value']
                    
                    # 如果参数还没有被设置，使用当前值
                    if param_key not in workflow_params:
                        workflow_params[param_key] = current_value
                    
                    # 根据参数类型显示不同的输入控件
                    if param_type == "text":
                        new_value = st.text_input(
                            f"{param_name}",
                            value=str(workflow_params[param_key]),
                            key=f"{workflow_key}_{param_key}"
                        )
                        workflow_params[param_key] = new_value
                        
                    elif param_type == "number":
                        if isinstance(current_value, int):
                            min_val = param.get('min_value', 0)
                            max_val = param.get('max_value', 100)
                            new_value = st.number_input(
                                f"{param_name}",
                                min_value=min_val,
                                max_value=max_val,
                                value=int(workflow_params[param_key]),
                                key=f"{workflow_key}_{param_key}"
                            )
                        else:
                            min_val = param.get('min_value', 0.0)
                            max_val = param.get('max_value', 100.0)
                            step = param.get('step', 0.1)
                            new_value = st.number_input(
                                f"{param_name}",
                                min_value=min_val,
                                max_value=max_val,
                                value=float(workflow_params[param_key]),
                                step=step,
                                key=f"{workflow_key}_{param_key}"
                            )
                        workflow_params[param_key] = new_value
                        
                    elif param_type == "checkbox":
                        new_value = st.checkbox(
                            f"{param_name}",
                            value=bool(workflow_params[param_key]),
                            key=f"{workflow_key}_{param_key}"
                        )
                        workflow_params[param_key] = new_value
                        
                    elif param_type == "seed":
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            new_value = st.number_input(
                                f"{param_name}",
                                min_value=-1,
                                max_value=2147483647,
                                value=int(workflow_params[param_key]),
                                key=f"{workflow_key}_{param_key}"
                            )
                        with col2:
                            if st.button("🎲 随机", key=f"{workflow_key}_{param_key}_random"):
                                import random
                                new_value = random.randint(0, 2147483647)
                                workflow_params[param_key] = new_value
                                st.rerun()
                        workflow_params[param_key] = new_value
        
        # 更新session state
        st.session_state[f"{workflow_key}_params"] = workflow_params
    
    # 输入输出目录设置
    st.markdown("---")
    st.header("📁 输入输出目录设置")
    
    # 安全获取工作流key和默认目录
    default_input_dir = safe_get_workflow_attr(workflow, 'metadata.default_input_dir', '')
    default_output_dir = safe_get_workflow_attr(workflow, 'metadata.default_output_dir', '')
    
    # 根据工作流类型显示不同的设置选项
    if workflow_type in ["图生图", "图片编辑", "图生视频"]:
        # 需要图片输入的工作流
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**📥 输入图片目录**")
            
            input_dir_key = f"{workflow_key}_input_dir"
            if input_dir_key not in st.session_state:
                st.session_state[input_dir_key] = default_input_dir
            
            current_input_dir = st.text_input(
                "输入目录路径",
                value=st.session_state[input_dir_key],
                key=f"{input_dir_key}_input",
                help="将需要处理的图片放在此目录中"
            )
            
            col1_1, col1_2 = st.columns(2)
            with col1_1:
                if st.button("📂 浏览选择", key=f"{input_dir_key}_browse"):
                    # 这里可以集成文件夹选择对话框
                    st.info("请在上方输入框中直接输入目录路径")
            
            with col1_2:
                if st.button("📁 创建目录", key=f"{input_dir_key}_create"):
                    if current_input_dir:
                        try:
                            os.makedirs(current_input_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_input_dir}")
                            st.session_state[input_dir_key] = current_input_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态
            if current_input_dir and os.path.exists(current_input_dir):
                try:
                    files = os.listdir(current_input_dir)
                    image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                    st.success(f"✅ 目录存在，包含 {len(image_files)} 个图片文件")
                except:
                    st.warning("⚠️ 无法读取目录内容")
            elif current_input_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
        
        with col2:
            st.markdown("**📤 输出目录**")
            
            output_dir_key = f"{workflow_key}_output_dir"
            if output_dir_key not in st.session_state:
                st.session_state[output_dir_key] = default_output_dir
            
            current_output_dir = st.text_input(
                "输出目录路径",
                value=st.session_state[output_dir_key],
                key=f"{output_dir_key}_input",
                help="处理后的图片/视频将保存在此目录中"
            )
            
            col2_1, col2_2 = st.columns(2)
            with col2_1:
                if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                    st.info("请在上方输入框中直接输入目录路径")
            
            with col2_2:
                if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                    if current_output_dir:
                        try:
                            os.makedirs(current_output_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_output_dir}")
                            st.session_state[output_dir_key] = current_output_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态
            if current_output_dir and os.path.exists(current_output_dir):
                st.success("✅ 输出目录已就绪")
            elif current_output_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    elif workflow_type in ["文生图", "文生视频"]:
        # 只需要输出目录的工作流
        st.markdown("**📤 输出目录设置**")
        
        output_type = "视频" if workflow_type == "文生视频" else "图片"
        st.info(f"此工作流会生成{output_type}，请设置输出目录")
        
        output_dir_key = f"{workflow_key}_output_dir"
        if output_dir_key not in st.session_state:
            st.session_state[output_dir_key] = default_output_dir
        
        current_output_dir = st.text_input(
            f"输出目录路径",
            value=st.session_state[output_dir_key],
            key=f"{output_dir_key}_input",
            help=f"生成的{output_type}将保存在此目录中"
        )
        
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                st.info("请在上方输入框中直接输入目录路径")
        
        with col2:
            if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                if current_output_dir:
                    try:
                        os.makedirs(current_output_dir, exist_ok=True)
                        st.success(f"目录已创建: {current_output_dir}")
                        st.session_state[output_dir_key] = current_output_dir
                    except Exception as e:
                        st.error(f"创建目录失败: {str(e)}")
        
        with col3:
            if current_output_dir and os.path.exists(current_output_dir):
                if st.button("📂 打开目录", key=f"{output_dir_key}_open"):
                    try:
                        if os.name == "nt":  # Windows
                            os.startfile(current_output_dir)
                        else:  # Linux/Mac
                            import subprocess
                            subprocess.run(["xdg-open", current_output_dir])
                        st.success("已在文件管理器中打开目录")
                    except:
                        st.warning("无法打开目录")
        
        # 显示目录状态
        if current_output_dir and os.path.exists(current_output_dir):
            st.success("✅ 输出目录已就绪")
        elif current_output_dir:
            st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    else:
        st.info("🔍 工作流类型未明确，请手动设置输入输出目录")
    
    # 运行控制
    st.markdown("---")
    st.header("🚀 运行工作流")
    
    # 获取当前设置的目录
    output_dir = st.session_state.get(f"{workflow_key}_output_dir", "")
    input_dir = st.session_state.get(f"{workflow_key}_input_dir", "")
    
    # 检查目录设置和有效性
    can_run = True
    issues = []
    
    if workflow_type in ["图生图", "图片编辑", "图生视频"]:
        if not input_dir:
            can_run = False
            issues.append("未设置输入目录")
        elif not os.path.exists(input_dir):
            can_run = False
            issues.append("输入目录不存在")
        else:
            # 检查输入目录中是否有图片文件
            try:
                files = os.listdir(input_dir)
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                if not image_files:
                    issues.append("⚠️ 输入目录中没有找到图片文件")
            except:
                can_run = False
                issues.append("无法读取输入目录")
    
    if not output_dir:
        can_run = False
        issues.append("未设置输出目录")
    elif not os.path.exists(output_dir):
        issues.append("⚠️ 输出目录不存在（运行时会自动创建）")
    
    # 检查是否是开发工作流
    tags = safe_get_workflow_attr(workflow, 'metadata.tags', [])
    is_dev_workflow = "DEV" in tags
    
    if is_dev_workflow:
        can_run = False
        issues.append("这是开发中的工作流，仅供查看")
    
    # 显示状态
    if can_run and not issues:
        st.success("✅ 所有设置都已就绪，可以运行工作流")
    elif issues:
        st.warning("⚠️ 检测到以下问题：")
        for issue in issues:
            st.write(f"• {issue}")
    
    # 运行选项
    col1, col2, col3 = st.columns(3)
    
    with col1:
        run_mode = st.selectbox(
            "运行模式",
            ["单次运行", "批量处理"],
            help="单次运行：处理单个文件或使用固定参数；批量处理：处理目录中的所有文件"
        )
    
    with col2:
        priority = st.selectbox(
            "优先级",
            ["普通", "高", "低"],
            help="任务在队列中的优先级"
        )
    
    with col3:
        save_intermediate = st.checkbox(
            "保存中间结果",
            value=False,
            help="保存工作流中间步骤的输出结果"
        )
    
    # 运行按钮
    button_col1, button_col2, button_col3 = st.columns([2, 1, 1])
    
    with button_col1:
        if st.button(
            "🚀 开始运行",
            disabled=not can_run,
            type="primary",
            use_container_width=True
        ):
            if not can_run:
                st.error("请先解决上述问题再运行工作流")
            else:
                # 准备运行参数
                run_params = {
                    "workflow_key": workflow_key,
                    "parameters": workflow_params,
                    "input_dir": input_dir,
                    "output_dir": output_dir,
                    "run_mode": run_mode,
                    "priority": priority,
                    "save_intermediate": save_intermediate
                }
                
                # 这里应该调用后端API运行工作流
                st.info("🔄 正在提交任务到运行队列...")
                # TODO: 实现实际的API调用
                st.success("✅ 任务已提交，可以在任务监控页面查看进度")
    
    with button_col2:
        if st.button("🔍 验证设置", use_container_width=True):
            st.info("🔄 正在验证工作流设置...")
            # TODO: 实现参数验证逻辑
            if workflow_params:
                st.success("✅ 参数设置验证通过")
            else:
                st.success("✅ 工作流使用默认参数")
    
    with button_col3:
        if st.button("💾 保存配置", use_container_width=True):
            # 保存当前配置到用户设置
            config_to_save = {
                "workflow_key": workflow_key,
                "parameters": workflow_params,
                "input_dir": input_dir,
                "output_dir": output_dir,
                "last_updated": datetime.now().isoformat()
            }
            
            # TODO: 实现配置保存逻辑
            st.success("✅ 配置已保存")
    
    # 运行历史
    if st.checkbox("显示运行历史", value=False):
        st.markdown("**📋 最近的运行记录**")
        # TODO: 从后端获取运行历史
        st.info("暂无运行记录")
