import streamlit as st
import httpx
import asyncio
from typing import Dict, Any, Optional, List
import os
from datetime import datetime
import json
import subprocess
import tkinter as tk
from tkinter import filedialog
import threading
import requests

# 页面配置
st.set_page_config(
    page_title="FreemanWorkHub",
    page_icon="🎨",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局变量
API_BASE_URL = "http://localhost:18001"

def check_auth():
    """检查用户是否已登录"""
    if "logged_in" not in st.session_state or not st.session_state.logged_in:
        show_login_page()
        return False
    return True

def show_login_page():
    """显示登录页面"""
    st.title("🔐 用户登录")
    
    with st.form("login_form"):
        username = st.text_input("用户名", placeholder="请输入用户名")
        access_key = st.text_input("访问密钥", type="password", placeholder="请输入访问密钥")
        
        submitted = st.form_submit_button("登录", type="primary", use_container_width=True)
        
        if submitted:
            if username and access_key:
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/login",
                        json={"username": username, "access_key": access_key}
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        st.session_state.logged_in = True
                        st.session_state.access_token = data.get("access_token")
                        st.session_state.user_info = data.get("user")
                        st.success("✅ 登录成功！")
                        st.rerun()
                    else:
                        st.error(f"❌ 登录失败: {response.text}")
                        
                except Exception as e:
                    st.error(f"❌ 连接失败: {str(e)}")
            else:
                st.error("❌ 请填写用户名和访问密钥")

def show_home_page():
    """显示主页"""
    st.title("🏠 FreemanWorkHub")
    st.markdown("欢迎使用智能工作流管理平台")
    
    # 状态概览
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("可用工作流", "5", delta="2")
    
    with col2:
        st.metric("运行中任务", "0", delta="0")
    
    with col3:
        st.metric("完成任务", "12", delta="3")
    
    with col4:
        st.metric("系统状态", "正常", delta="良好")
    
    # 快速开始
    st.markdown("---")
    st.subheader("🚀 快速开始")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("⚙️ 浏览工作流", use_container_width=True, type="primary"):
            st.session_state.page = "workflows"
            st.rerun()
    
    with col2:
        if st.button("📊 任务监控", use_container_width=True):
            st.session_state.page = "monitor"
            st.rerun()

def show_workflows_page():
    """显示工作流列表页面"""
    st.title("⚙️ 工作流管理")
    
    # 获取工作流列表
    try:
        response = requests.get(
            f"{API_BASE_URL}/workflows",
            headers={"Authorization": f"Bearer {st.session_state.get('access_token', '')}"}
        )
        
        if response.status_code == 200:
            workflows_data = response.json()
            workflows = workflows_data.get("workflows", [])
            
            if not workflows:
                st.info("📋 暂无可用的工作流")
                return
            
            # 搜索和筛选
            col1, col2 = st.columns(2)
            with col1:
                search_term = st.text_input("🔍 搜索工作流", placeholder="输入工作流名称或标签...")
            with col2:
                tag_filter = st.selectbox("🏷️ 按标签筛选", ["全部", "RDY", "DEV", "其他"])
            
            # 筛选工作流
            filtered_workflows = workflows
            if search_term:
                filtered_workflows = [w for w in filtered_workflows 
                                    if search_term.lower() in w.get('key', '').lower() 
                                    or search_term.lower() in w.get('metadata', {}).get('name', '').lower()]
            
            if tag_filter != "全部":
                filtered_workflows = [w for w in filtered_workflows 
                                    if tag_filter in w.get('metadata', {}).get('tags', [])]
            
            # 显示工作流卡片
            st.markdown(f"找到 {len(filtered_workflows)} 个工作流")
            
            cols = st.columns(2)
            for i, workflow in enumerate(filtered_workflows):
                with cols[i % 2]:
                    show_workflow_card(workflow)
                    
        else:
            st.error(f"❌ 获取工作流列表失败: {response.status_code}")
            
    except Exception as e:
        st.error(f"❌ 连接后端失败: {str(e)}")

def show_workflow_card(workflow):
    """显示工作流卡片"""
    metadata = workflow.get('metadata', {})
    name = metadata.get('name', workflow.get('key', '未知工作流'))
    description = metadata.get('description', '暂无描述')
    tags = metadata.get('tags', [])
    version = metadata.get('version', '1.0')
    
    with st.container():
        st.markdown(f"""
        <div style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin: 10px 0;">
            <h4>{name}</h4>
            <p style="color: #666;">{description}</p>
        """, unsafe_allow_html=True)
        
        # 标签
        if tags:
            tag_colors = {"RDY": "🟢", "DEV": "🟡", "BETA": "🔵"}
            tag_display = " ".join([f"{tag_colors.get(tag, '⚪')} {tag}" for tag in tags])
            st.markdown(f"**标签:** {tag_display}")
        
        st.markdown(f"**版本:** {version}")
        
        # 按钮
        if st.button(f"🎯 转到工作流", key=f"go_to_{workflow.get('key')}", use_container_width=True):
            st.session_state.current_workflow_key = workflow.get('key')
            st.session_state.page = "workflow_config"
            st.rerun()
        
        st.markdown("</div>", unsafe_allow_html=True)

def show_workflow_config_page():
    """显示工作流配置页面"""
    workflow_key = st.session_state.get("current_workflow_key")
    if not workflow_key:
        st.error("❌ 没有选择工作流")
        st.session_state.page = "workflows"
        st.rerun()
        return
    
    # 返回按钮
    if st.button("← 返回工作流列表"):
        st.session_state.page = "workflows"
        st.rerun()
        return
    
    # 尝试使用工作流配置模块
    try:
        # 检查模块文件是否存在
        config_file = os.path.join(os.path.dirname(__file__), "workflow_config.py")
        if os.path.exists(config_file):
            from .workflow_config import show_workflow_config_page as show_config
            show_config(workflow_key)
        else:
            # 使用简化版本
            show_simple_workflow_config(workflow_key)
    except ImportError as e:
        st.warning(f"⚠️ 无法加载工作流配置模块: {str(e)}")
        show_simple_workflow_config(workflow_key)

def show_simple_workflow_config(workflow_key):
    """显示简化的工作流配置页面"""
    st.title(f"🎯 工作流配置：{workflow_key}")
    
    # 获取工作流数据
    try:
        response = requests.get(
            f"{API_BASE_URL}/workflows/{workflow_key}",
            headers={"Authorization": f"Bearer {st.session_state.get('access_token', '')}"}
        )
        
        if response.status_code == 200:
            workflow_data = response.json()
            workflow = workflow_data.get("workflow")
            
            if not workflow:
                st.error("❌ 工作流数据为空")
                return
            
            # 基本信息
            st.header("📋 工作流信息")
            metadata = workflow.get('metadata', {})
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"**名称：** {metadata.get('name', workflow_key)}")
                st.markdown(f"**版本：** {metadata.get('version', '1.0')}")
                st.markdown(f"**作者：** {metadata.get('author', '未知')}")
            
            with col2:
                tags = metadata.get('tags', [])
                if tags:
                    st.markdown(f"**标签：** {', '.join(tags)}")
                description = metadata.get('description', '暂无描述')
                st.markdown(f"**描述：** {description}")
            
            # 参数配置
            st.markdown("---")
            st.header("⚙️ 参数配置")
            
            # 检查是否有可编辑参数
            editable_params = metadata.get('editable_params', [])
            if editable_params:
                st.info(f"发现 {len(editable_params)} 个可配置参数")
                
                # 参数编辑区域
                for param in editable_params:
                    param_name = param.get('name', '未知参数')
                    param_type = param.get('type', 'text')
                    default_value = param.get('default', '')
                    
                    if param_type == 'text':
                        st.text_input(param_name, value=default_value)
                    elif param_type == 'number':
                        st.number_input(param_name, value=float(default_value) if default_value else 0.0)
                    elif param_type == 'select':
                        options = param.get('options', [])
                        st.selectbox(param_name, options)
            else:
                st.info("😊 此工作流使用默认参数，无需额外配置")
            
            # 目录设置
            st.markdown("---")
            st.header("📁 目录设置")
            
            col1, col2 = st.columns(2)
            with col1:
                st.text_input("输入目录", placeholder="选择输入文件目录...")
                if st.button("📂 浏览", key="input_browse"):
                    st.info("请手动输入目录路径")
            
            with col2:
                st.text_input("输出目录", placeholder="选择输出文件目录...")
                if st.button("📂 浏览", key="output_browse"):
                    st.info("请手动输入目录路径")
            
            # 运行控制
            st.markdown("---")
            st.header("🚀 运行控制")
            
            # 检查是否是开发工作流
            is_dev = "DEV" in tags
            
            if is_dev:
                st.warning("⚠️ 这是开发中的工作流，仅供查看")
                st.button("▶️ 开始运行", disabled=True, help="开发中的工作流无法运行")
            else:
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    if st.button("▶️ 开始运行", type="primary", use_container_width=True):
                        st.success("✅ 配置验证通过")
                        st.info("🚧 运行功能开发中...")
                
                with col2:
                    if st.button("💾 保存配置", use_container_width=True):
                        st.success("✅ 配置已保存")
                
                with col3:
                    if st.button("🔄 重置配置", use_container_width=True):
                        st.success("✅ 配置已重置")
            
        else:
            st.error(f"❌ 获取工作流失败: {response.status_code}")
            
    except Exception as e:
        st.error(f"❌ 连接后端失败: {str(e)}")

def show_pipeline_page():
    """显示流水线编排页面"""
    st.title("🔗 流水线编排")
    st.info("流水线编排功能开发中...")
    
    if st.button("返回主页"):
        st.session_state.page = "home"
        st.rerun()

def main():
    """主函数"""
    # 初始化页面状态
    if "page" not in st.session_state:
        st.session_state.page = "home"
    
    # 检查登录状态
    if not check_auth():
        return
    
    # 侧边栏导航
    with st.sidebar:
        st.title("🧭 导航")
        
        # 显示登录状态
        if "logged_in" in st.session_state and st.session_state.logged_in:
            username = st.session_state.user_info.get("username", "用户")
            st.success(f"✅ 已登录: {username}")
            
            if st.button("🚪 退出登录", use_container_width=True):
                st.session_state.clear()
                st.rerun()
        
        st.markdown("---")
        
        # 导航按钮
        if st.button("🏠 主页", use_container_width=True):
            st.session_state.page = "home"
            st.rerun()
        
        if st.button("⚙️ 工作流管理", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()
        
        if st.button("🔗 流水线编排", use_container_width=True):
            st.session_state.page = "pipeline"
            st.rerun()
        
        if st.button("📊 任务监控", use_container_width=True):
            st.session_state.page = "monitor"
            st.rerun()
        
        if st.button("📁 输出管理", use_container_width=True):
            st.session_state.page = "outputs"
            st.rerun()
    
    # 页面路由
    if st.session_state.page == "home":
        show_home_page()
    elif st.session_state.page == "workflows":
        show_workflows_page()
    elif st.session_state.page == "workflow_config":
        show_workflow_config_page()
    elif st.session_state.page == "pipeline":
        show_pipeline_page()
    elif st.session_state.page == "monitor":
        st.title("📊 任务监控")
        st.info("监控功能开发中...")
        if st.button("返回主页"):
            st.session_state.page = "home"
            st.rerun()
    elif st.session_state.page == "outputs":
        st.title("📁 输出管理")
        st.info("输出管理功能开发中...")
        if st.button("返回主页"):
            st.session_state.page = "home"
            st.rerun()
    else:
        show_home_page()

if __name__ == "__main__":
    main()
