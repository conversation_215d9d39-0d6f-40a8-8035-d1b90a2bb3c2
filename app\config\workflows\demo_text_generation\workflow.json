{"3": {"inputs": {"text": "测试提示词 - Debug Test", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "正面提示词"}}, "4": {"inputs": {"ckpt_name": "v1-5-pruned-emaonly.ckpt"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "模型加载器"}}, "7": {"inputs": {"text": "低质量，模糊", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "负面提示词"}}, "10": {"inputs": {"seed": 156680208700286, "steps": 25, "cfg": 7.5, "sampler_name": "euler", "scheduler": "normal", "denoise": 1.0, "model": ["4", 0], "positive": ["3", 0], "negative": ["7", 0], "latent_image": ["11", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "采样器"}}, "11": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空白潜在图像"}}, "12": {"inputs": {"samples": ["10", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "13": {"inputs": {"filename_prefix": "ComfyUI", "images": ["12", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}