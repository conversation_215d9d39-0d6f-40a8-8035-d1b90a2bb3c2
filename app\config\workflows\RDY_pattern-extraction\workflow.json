{"3": {"inputs": {"text": "一张高质量的图像", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "正面提示词"}}, "4": {"inputs": {"ckpt_name": "flux-model.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "模型加载器"}}, "5": {"inputs": {"guidance": 2.5, "conditioning": ["3", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导参数"}}, "6": {"inputs": {"seed": 42, "steps": 20, "cfg": 1.0, "sampler_name": "euler", "scheduler": "simple", "denoise": 1.0, "model": ["4", 0], "positive": ["5", 0], "negative": ["7", 0], "latent_image": ["8", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "采样器"}}, "7": {"inputs": {"text": "", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "负面提示词"}}, "8": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空白潜在图像"}}, "9": {"inputs": {"samples": ["6", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "10": {"inputs": {"filename_prefix": "pattern_extraction", "images": ["9", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "16": {"widgets_values": [4.0]}}